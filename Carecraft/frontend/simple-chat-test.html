<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .chat-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
        }
        
        .messages {
            height: 400px;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .message {
            max-width: 80%;
            padding: 0.75rem 1rem;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .message.agent {
            align-self: flex-start;
            background: #f0f0f0;
            color: #333;
        }
        
        .input-container {
            padding: 1rem;
            border-top: 1px solid #eee;
            display: flex;
            gap: 0.5rem;
        }
        
        .input-container input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }
        
        .input-container button {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
        }
        
        .input-container button:hover {
            opacity: 0.9;
        }
        
        .input-container button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 0.5rem 1rem;
            background: #e8f5e8;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
            color: #666;
        }
        
        .status.disconnected {
            background: #ffe8e8;
            color: #d00;
        }
        
        .typing {
            font-style: italic;
            color: #999;
        }
    </style>
</head>
<body>
    <h1>Simple Chat Interface Test</h1>
    <p>This is a test interface for the simple chat WebSocket endpoint.</p>
    
    <div class="chat-container">
        <div class="chat-header">
            <h2>Emergency Chat</h2>
            <div id="sessionId">Session: Not connected</div>
        </div>
        
        <div class="messages" id="messages">
            <!-- Messages will appear here -->
        </div>
        
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message..." disabled>
            <button id="sendButton" disabled>Send</button>
        </div>
        
        <div class="status" id="status">Disconnected</div>
    </div>
    
    <script>
        let ws = null;
        let sessionId = null;
        
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusDiv = document.getElementById('status');
        const sessionIdDiv = document.getElementById('sessionId');
        
        function generateSessionId() {
            return `simple-chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }
        
        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'agent'}`;
            messageDiv.textContent = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function updateStatus(text, isConnected = false) {
            statusDiv.textContent = text;
            statusDiv.className = `status ${isConnected ? '' : 'disconnected'}`;
        }
        
        function connectWebSocket() {
            sessionId = generateSessionId();
            sessionIdDiv.textContent = `Session: ${sessionId.slice(-8)}`;
            
            const wsUrl = `ws://localhost:8000/simple-chat/${sessionId}`;
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                console.log('WebSocket connected');
                updateStatus('Connected', true);
                messageInput.disabled = false;
                sendButton.disabled = false;
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                console.log('Received:', data);
                
                if (data.type === 'agent_message') {
                    addMessage(data.message, false);
                } else if (data.type === 'connection_established') {
                    console.log('Connection established for session:', data.session_id);
                }
            };
            
            ws.onclose = function() {
                console.log('WebSocket disconnected');
                updateStatus('Disconnected', false);
                messageInput.disabled = true;
                sendButton.disabled = true;
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                updateStatus('Connection error', false);
            };
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !ws) return;
            
            // Add user message to chat
            addMessage(message, true);
            
            // Send message via WebSocket
            ws.send(JSON.stringify({
                type: 'user_message',
                message: message
            }));
            
            messageInput.value = '';
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Connect on page load
        connectWebSocket();
    </script>
</body>
</html>
