/* CareCraft Global Styles with B<PERSON><PERSON> Branding */

:root {
  /* BOSCH Brand Colors */
  --bosch-dark-blue: #013662;
  --bosch-dark-green: #237147;
  --bosch-red: #ee4949;
  
  /* Additional Colors */
  --white: #ffffff;
  --light-gray: #f5f5f5;
  --medium-gray: #e0e0e0;
  --dark-gray: #666666;
  --text-dark: #333333;
  --text-light: #666666;
  
  /* Typography */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.6;
  font-weight: 400;
  
  /* Shadows */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
  
  /* Border Radius */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  color-scheme: light;
  color: var(--text-dark);
  background-color: var(--white);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--light-gray);
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--bosch-dark-blue);
  margin: 0 0 var(--spacing-md) 0;
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-light);
}

/* Links */
a {
  color: var(--bosch-dark-blue);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.25s ease;
}

a:hover {
  color: var(--bosch-red);
}

/* Buttons */
button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  border: 2px solid transparent;
  border-radius: var(--radius-medium);
  background-color: var(--bosch-dark-blue);
  color: var(--white);
  cursor: pointer;
  transition: all 0.25s ease;
  text-transform: none;
  min-height: 44px;
}

button:hover {
  background-color: var(--bosch-dark-green);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-light);
}

button:focus,
button:focus-visible {
  outline: 2px solid var(--bosch-red);
  outline-offset: 2px;
}

button:disabled {
  background-color: var(--medium-gray);
  color: var(--dark-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Secondary Button Variant */
button.secondary {
  background-color: transparent;
  color: var(--bosch-dark-blue);
  border-color: var(--bosch-dark-blue);
}

button.secondary:hover {
  background-color: var(--bosch-dark-blue);
  color: var(--white);
}

/* Danger Button Variant */
button.danger {
  background-color: var(--bosch-red);
}

button.danger:hover {
  background-color: #d73d3d;
}

/* Form Elements */
input, textarea, select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
  font-family: inherit;
  border: 2px solid var(--medium-gray);
  border-radius: var(--radius-medium);
  background-color: var(--white);
  color: var(--text-dark);
  transition: border-color 0.25s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--bosch-dark-blue);
  box-shadow: 0 0 0 3px rgba(1, 54, 98, 0.1);
}

/* Utility Classes */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* Card Component */
.card {
  background-color: var(--white);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-xl);
  border: 1px solid var(--medium-gray);
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

/* Loading Animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading {
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    font-size: 14px;
  }
  
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  :root {
    font-size: 13px;
  }
  
  button {
    padding: var(--spacing-sm);
    font-size: 0.9rem;
  }
}
