/* CareCraft Application Styles */

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Main Page Layout */
.main-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--light-gray);
}

.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background-color: var(--white);
  border-bottom: 3px solid var(--bosch-dark-blue);
  box-shadow: var(--shadow-light);
}

.logo {
  color: var(--bosch-dark-blue);
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -1px;
}

.reset-button {
  background-color: var(--bosch-red);
  color: var(--white);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-medium);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s ease;
}

.reset-button:hover {
  background-color: #d73d3d;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.main-content {
  flex: 1;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Phase Components */
.preflight,
.flight-control,
.post-flight {
  background-color: var(--white);
  border-radius: var(--radius-large);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-medium);
  margin-bottom: var(--spacing-xl);
  border-left: 5px solid var(--bosch-dark-blue);
}

.preflight h2,
.flight-control h2,
.post-flight h2 {
  color: var(--bosch-dark-blue);
  margin-bottom: var(--spacing-lg);
  font-size: 2rem;
  font-weight: 600;
}

.preflight {
  border-left-color: var(--bosch-dark-green);
}

.flight-control {
  border-left-color: var(--bosch-red);
}

.post-flight {
  border-left-color: var(--bosch-dark-blue);
}

/* Controls */
.controls {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  flex-wrap: wrap;
}

.controls button {
  flex: 1;
  min-width: 120px;
}

/* Placeholder Data */
.placeholder-data {
  background-color: var(--light-gray);
  padding: var(--spacing-lg);
  border-radius: var(--radius-medium);
  margin-top: var(--spacing-lg);
  border: 1px solid var(--medium-gray);
}

.placeholder-data p {
  margin-bottom: var(--spacing-sm);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: var(--text-dark);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator.active {
  background-color: rgba(238, 73, 73, 0.1);
  color: var(--bosch-red);
}

.status-indicator.completed {
  background-color: rgba(35, 113, 71, 0.1);
  color: var(--bosch-dark-green);
}

.status-indicator.pending {
  background-color: rgba(1, 54, 98, 0.1);
  color: var(--bosch-dark-blue);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--medium-gray);
  border-radius: var(--radius-small);
  overflow: hidden;
  margin: var(--spacing-md) 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--bosch-dark-blue), var(--bosch-dark-green));
  transition: width 0.3s ease;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-header {
    padding: var(--spacing-md);
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .logo {
    font-size: 2rem;
  }
  
  .main-content {
    padding: var(--spacing-md);
  }
  
  .preflight,
  .flight-control,
  .post-flight {
    padding: var(--spacing-lg);
  }
  
  .controls {
    flex-direction: column;
  }
  
  .controls button {
    flex: none;
  }
}

@media (max-width: 480px) {
  .main-header {
    padding: var(--spacing-sm);
  }
  
  .logo {
    font-size: 1.5rem;
  }
  
  .main-content {
    padding: var(--spacing-sm);
  }
  
  .preflight,
  .flight-control,
  .post-flight {
    padding: var(--spacing-md);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .slide-in-left,
  .slide-in-right {
    animation: none;
  }
  
  button {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .main-header {
    border-bottom-width: 5px;
  }
  
  .preflight,
  .flight-control,
  .post-flight {
    border-left-width: 8px;
    border: 2px solid var(--text-dark);
  }
  
  button {
    border: 2px solid currentColor;
  }
}
