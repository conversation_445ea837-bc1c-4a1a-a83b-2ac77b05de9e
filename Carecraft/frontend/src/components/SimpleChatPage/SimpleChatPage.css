.simple-chat-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  padding: 2rem 1rem 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.page-header h1 {
  margin: 0 0 0.5rem;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  margin: 0;
  font-size: 1.1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.chat-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.start-session-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 500px;
}

.start-session-card {
  background: white;
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.start-session-card h2 {
  margin: 0 0 1rem;
  font-size: 1.75rem;
  font-weight: 600;
  color: #1f2937;
}

.start-session-card p {
  margin: 0 0 2rem;
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
}

.start-session-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.start-session-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.start-session-button:active {
  transform: translateY(0);
}

.session-history {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.session-history h3 {
  margin: 0 0 1rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
}

.session-history ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.session-history li {
  padding: 0.5rem 0;
  font-size: 0.9rem;
  color: #6b7280;
  border-bottom: 1px solid #f3f4f6;
}

.session-history li:last-child {
  border-bottom: none;
}

.page-footer {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.page-footer p {
  margin: 0;
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .page-header {
    padding: 1.5rem 1rem 0.75rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .page-header p {
    font-size: 1rem;
  }
  
  .chat-section {
    padding: 1rem;
  }
  
  .start-session-card {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }
  
  .start-session-card h2 {
    font-size: 1.5rem;
  }
  
  .start-session-button {
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.75rem;
  }
  
  .start-session-card {
    padding: 1.5rem 1rem;
  }
  
  .start-session-card h2 {
    font-size: 1.25rem;
  }
  
  .start-session-button {
    width: 100%;
    padding: 1rem;
  }
}
