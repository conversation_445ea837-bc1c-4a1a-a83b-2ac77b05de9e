import React, { useState } from 'react';
import SimpleChat from '../SimpleChat';
import './SimpleChatPage.css';

const SimpleChatPage: React.FC = () => {
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [sessionHistory, setSessionHistory] = useState<string[]>([]);

  const handleSessionStart = (sessionId: string) => {
    console.log('New session started:', sessionId);
    setCurrentSessionId(sessionId);
  };

  const handleSessionEnd = () => {
    console.log('Session ended:', currentSessionId);
    if (currentSessionId) {
      setSessionHistory(prev => [...prev, currentSessionId]);
    }
    setCurrentSessionId('');
  };

  const startNewSession = () => {
    // Generate a new session ID
    const newSessionId = `simple-chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    setCurrentSessionId(newSessionId);
  };

  return (
    <div className="simple-chat-page">
      <div className="page-header">
        <h1>Emergency Communication System</h1>
        <p>Simple conversation interface for Speech-to-Text and Text-to-Speech integration</p>
      </div>

      <div className="chat-section">
        {currentSessionId ? (
          <SimpleChat
            sessionId={currentSessionId}
            onSessionStart={handleSessionStart}
            onSessionEnd={handleSessionEnd}
          />
        ) : (
          <div className="start-session-container">
            <div className="start-session-card">
              <h2>Start New Emergency Session</h2>
              <p>Click below to begin a new emergency communication session.</p>
              <button onClick={startNewSession} className="start-session-button">
                Start New Session
              </button>
              
              {sessionHistory.length > 0 && (
                <div className="session-history">
                  <h3>Previous Sessions</h3>
                  <ul>
                    {sessionHistory.slice(-5).map((sessionId, index) => (
                      <li key={index}>
                        Session: {sessionId.slice(-8)}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="page-footer">
        <p>This interface shows only the conversation between user and agent, without system calls or technical details.</p>
      </div>
    </div>
  );
};

export default SimpleChatPage;
