import React, { useEffect, useRef, useState } from 'react';
// Fix import for AudioMotionAnalyzer - use dynamic import as fallback
import AudioMotionAnalyzer from 'audiomotion-analyzer';
import './VoiceVisualizer.css';

interface VoiceVisualizerProps {
  isActive: boolean;
  audioStream?: MediaStream;
  label: string;
  type: 'user' | 'bot';
}

const VoiceVisualizer: React.FC<VoiceVisualizerProps> = ({
  isActive,
  audioStream,
  label,
  type
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const audioMotionRef = useRef<AudioMotionAnalyzer | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const oscillatorRef = useRef<OscillatorNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  const [volume, setVolume] = useState(0);
  const [isDemoMode, setIsDemoMode] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    if (!containerRef.current) {
      console.warn('[VoiceVisualizer] Container ref not available');
      return;
    }
    
    // Add a small delay to ensure the container is properly rendered
    const initTimeout = setTimeout(() => {
      initializeAudioMotion();
    }, 100);
    
    return () => {
      clearTimeout(initTimeout);
      cleanup();
    };
  }, [type]);

  const initializeAudioMotion = async () => {
    if (!containerRef.current) {
      setInitError('Container element not available');
      return;
    }
    
    // Check container dimensions
    const rect = containerRef.current.getBoundingClientRect();
    
    if (rect.width === 0 || rect.height === 0) {
      console.warn('[VoiceVisualizer] Container has zero dimensions, retrying...');
      setTimeout(() => initializeAudioMotion(), 200);
      return;
    }

    // Initialize AudioMotion analyzer
    try {
      setInitError(null);
      const audioMotion = new AudioMotionAnalyzer(containerRef.current, {
        mode: 3, // 1/3 octave bands
        barSpace: 0.3,
        bgAlpha: 0,
        colorMode: 'gradient',
        gradient: 'classic', // Use a valid default gradient initially
        height: 120,
        ledBars: false,
        lineWidth: 2,
        lumiBars: false,
        maxFreq: 16000,
        minFreq: 20,
        mirror: 0,
        noteLabels: false,
        outlineBars: false,
        radial: false,
        reflexRatio: 0,
        showBgColor: false,
        showFPS: false,
        showPeaks: true,
        showScaleX: false,
        showScaleY: false,
        smoothing: 0.7,
        spinSpeed: 0,
        splitGradient: false,
        weightingFilter: 'A'
      });

      // Custom gradient for BOSCH branding
      const customGradient = {
        name: type === 'user' ? 'boschUser' : 'boschBot',
        bgColor: '#1a1a1a',
        colorStops: type === 'user' 
          ? [
              { pos: 0, color: '#013662' },    // BOSCH dark blue
              { pos: 0.5, color: '#237147' },  // BOSCH dark green  
              { pos: 1, color: '#ffffff' }     // White peaks
            ]
          : [
              { pos: 0, color: '#ee4949' },    // BOSCH red
              { pos: 0.5, color: '#ff6b6b' },  // Lighter red
              { pos: 1, color: '#ffffff' }     // White peaks
            ]
      };

      try {
        audioMotion.registerGradient(customGradient.name, customGradient);
        audioMotion.gradient = customGradient.name;
      } catch (gradientError) {
        console.warn('[VoiceVisualizer] Custom gradient registration failed, using fallback:', gradientError);
        // Fallback to built-in gradients that actually exist
        audioMotion.gradient = type === 'user' ? 'rainbow' : 'classic';
      }

      audioMotionRef.current = audioMotion;

      // Use AudioMotion's built-in AudioContext instead of creating our own
      try {
        const audioContext = audioMotion.audioCtx;
        audioContextRef.current = audioContext;
        
        // Resume context if suspended (required by some browsers)
        if (audioContext.state === 'suspended') {
          audioContext.resume().catch(err => {
            console.error('[VoiceVisualizer] Failed to resume AudioContext:', err);
          });
        }
      } catch (contextError) {
        console.error('[VoiceVisualizer] AudioContext access failed:', contextError);
      }

      setIsInitialized(true);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[VoiceVisualizer] Error initializing AudioMotion:', error);
      console.error('[VoiceVisualizer] Error details:', {
        message: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        containerElement: containerRef.current,
        type: type
      });
      setInitError(`Initialization failed: ${errorMessage}`);
      setIsInitialized(false);
    }
  };

  useEffect(() => {
    if (isActive && isInitialized && audioMotionRef.current && audioContextRef.current) {
      if (audioStream) {
        // Use real audio stream
        connectRealAudio();
        setIsDemoMode(false);
      } else {
        // Use demo audio simulation
        startDemoAudio();
        setIsDemoMode(true);
      }
    } else {
      stopAudio();
    }
  }, [isActive, audioStream, isInitialized]);

  const connectRealAudio = () => {
    if (!audioStream || !audioContextRef.current || !audioMotionRef.current) {
      return;
    }

    try {
      const source = audioContextRef.current.createMediaStreamSource(audioStream);
      sourceNodeRef.current = source;
      audioMotionRef.current.connectInput(source);

      // Monitor volume levels
      const analyser = audioContextRef.current.createAnalyser();
      source.connect(analyser);
      
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const updateVolume = () => {
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
        setVolume(average / 255 * 100);
        
        if (isActive) {
          requestAnimationFrame(updateVolume);
        }
      };
      updateVolume();

    } catch (error) {
      console.error('[VoiceVisualizer] Error connecting real audio, falling back to demo:', error);
      startDemoAudio(); // Fallback to demo
    }
  };

  const startDemoAudio = () => {
    if (!audioContextRef.current || !audioMotionRef.current) {
      return;
    }

    try {
      // Ensure AudioContext is running
      if (audioContextRef.current.state === 'suspended') {
        audioContextRef.current.resume();
      }
      
      // Create oscillator for demo audio
      const oscillator = audioContextRef.current.createOscillator();
      const gainNode = audioContextRef.current.createGain();
      
      oscillatorRef.current = oscillator;
      gainNodeRef.current = gainNode;

      oscillator.connect(gainNode);
      audioMotionRef.current.connectInput(gainNode);

      // Configure oscillator based on type
      if (type === 'user') {
        // Simulate user speech patterns
        oscillator.frequency.setValueAtTime(200, audioContextRef.current.currentTime);
        oscillator.type = 'triangle';
        
        // Vary frequency to simulate speech
        const varyFrequency = () => {
          if (oscillatorRef.current && audioContextRef.current) {
            const baseFreq = 150 + Math.random() * 100;
            const time = audioContextRef.current.currentTime;
            oscillatorRef.current.frequency.setTargetAtTime(baseFreq, time, 0.1);
            
            if (isActive) {
              setTimeout(varyFrequency, 100 + Math.random() * 200);
            }
          }
        };
        varyFrequency();

      } else {
        // Simulate bot speech patterns
        oscillator.frequency.setValueAtTime(180, audioContextRef.current.currentTime);
        oscillator.type = 'sine';
        
        // More consistent frequency for bot
        const varyFrequency = () => {
          if (oscillatorRef.current && audioContextRef.current) {
            const baseFreq = 160 + Math.random() * 60;
            const time = audioContextRef.current.currentTime;
            oscillatorRef.current.frequency.setTargetAtTime(baseFreq, time, 0.05);
            
            if (isActive) {
              setTimeout(varyFrequency, 150 + Math.random() * 100);
            }
          }
        };
        varyFrequency();
      }

      // Vary gain to simulate volume changes
      const varyGain = () => {
        if (gainNodeRef.current && audioContextRef.current) {
          const gain = 0.1 + Math.random() * 0.3;
          gainNodeRef.current.gain.setTargetAtTime(gain, audioContextRef.current.currentTime, 0.1);
          setVolume(gain * 100);
          
          if (isActive) {
            setTimeout(varyGain, 50 + Math.random() * 100);
          }
        }
      };
      varyGain();

      oscillator.start();

    } catch (error) {
      console.error('[VoiceVisualizer] Error starting demo audio:', error);
    }
  };

  const stopAudio = () => {
    if (oscillatorRef.current) {
      try {
        oscillatorRef.current.stop();
      } catch {
        // Oscillator might already be stopped
      }
      oscillatorRef.current = null;
    }

    if (sourceNodeRef.current) {
      sourceNodeRef.current.disconnect();
      sourceNodeRef.current = null;
    }

    setVolume(0);
  };

  const cleanup = () => {
    stopAudio();
    
    if (audioMotionRef.current) {
      try {
        audioMotionRef.current.destroy();
        audioMotionRef.current = null;
      } catch (error) {
        console.error('[VoiceVisualizer] Error destroying AudioMotion:', error);
      }
    }

    // Don't close AudioMotion's AudioContext - it manages its own
    if (audioContextRef.current) {
      audioContextRef.current = null;
    }
  };

  const getStatusIcon = () => {
    if (!isActive) return '⏸️';
    if (isDemoMode) return '🎭';
    return volume > 10 ? '🔊' : '🔇';
  };

  const getStatusText = () => {
    if (!isActive) return 'Inactive';
    if (isDemoMode) return 'Demo Mode';
    return volume > 10 ? 'Active' : 'Quiet';
  };

  return (
    <div className={`voice-visualizer ${type} ${isActive ? 'active' : 'inactive'} ${!isInitialized ? 'loading' : ''}`}>
      <div className="visualizer-header">
        <h3 className="visualizer-label">{label}</h3>
        <div className="status-indicators">
          <span className="status-icon">{getStatusIcon()}</span>
          <span className="status-text">{getStatusText()}</span>
          <div className="volume-meter">
            <div 
              className="volume-fill" 
              style={{ width: `${volume}%` }}
            />
          </div>
        </div>
      </div>
      
      <div
        ref={containerRef}
        className="visualizer-container"
        style={{
          border: `2px solid ${type === 'user' ? 'var(--bosch-dark-blue)' : 'var(--bosch-red)'}`,
          borderRadius: 'var(--radius-medium)',
          backgroundColor: '#1a1a1a'
        }}
      >
        {!isInitialized && !initError && (
          <div className="loading-overlay">
            <span>Initializing visualizer...</span>
          </div>
        )}
        {initError && (
          <div className="error-overlay">
            <span>⚠️ {initError}</span>
          </div>
        )}
      </div>
      
      {isDemoMode && (
        <div className="demo-indicator">
          <span>🎭 Demo Mode - Simulated Audio</span>
        </div>
      )}
    </div>
  );
};

export default VoiceVisualizer;