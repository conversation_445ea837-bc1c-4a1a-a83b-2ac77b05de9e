.simple-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.simple-chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.simple-chat-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.connection-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.status-indicator {
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.connected {
  color: #4ade80;
}

.status-indicator.disconnected {
  color: #f87171;
}

.session-id {
  font-size: 0.75rem;
  opacity: 0.8;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  display: flex;
  max-width: 80%;
}

.message.user {
  align-self: flex-end;
}

.message.agent {
  align-self: flex-start;
}

.message-content {
  background: white;
  border-radius: 18px;
  padding: 0.75rem 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message.user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.agent .message-content {
  background: white;
  color: #333;
  border: 1px solid #e5e7eb;
}

.message-text {
  font-size: 0.95rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.message-timestamp {
  font-size: 0.75rem;
  opacity: 0.7;
  text-align: right;
}

.message.agent .message-timestamp {
  text-align: left;
}

.typing-indicator {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  padding: 0.5rem 0;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-container {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 1rem;
}

.input-wrapper {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.input-wrapper textarea {
  flex: 1;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  font-family: inherit;
  max-height: 120px;
  min-height: 44px;
}

.input-wrapper textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-wrapper textarea:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.send-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.session-controls {
  margin-top: 0.75rem;
  display: flex;
  justify-content: center;
}

.end-session-button {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.end-session-button:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .simple-chat-container {
    height: 100vh;
    border-radius: 0;
  }
  
  .simple-chat-header {
    padding: 0.75rem;
  }
  
  .simple-chat-header h2 {
    font-size: 1.25rem;
  }
  
  .messages-container {
    padding: 0.75rem;
  }
  
  .message {
    max-width: 90%;
  }
  
  .input-container {
    padding: 0.75rem;
  }
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
