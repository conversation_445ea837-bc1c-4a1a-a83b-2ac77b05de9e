import React, { useState, useEffect, useRef } from 'react';
import './SimpleChat.css';

interface Message {
  type: 'user_message' | 'agent_message';
  message: string;
  timestamp: string;
}

interface SimpleChatProps {
  sessionId?: string;
  onSessionStart?: (sessionId: string) => void;
  onSessionEnd?: () => void;
}

const SimpleChat: React.FC<SimpleChatProps> = ({
  sessionId: propSessionId,
  onSessionStart,
  onSessionEnd
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>(propSessionId || '');
  const [isComplete, setIsComplete] = useState(false);
  
  const wsRef = useRef<WebSocket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Generate session ID if not provided
  useEffect(() => {
    if (!sessionId) {
      const newSessionId = `simple-chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      setSessionId(newSessionId);
      onSessionStart?.(newSessionId);
    }
  }, [sessionId, onSessionStart]);

  // WebSocket connection
  useEffect(() => {
    if (!sessionId) return;

    const connectWebSocket = () => {
      const wsUrl = `ws://localhost:8000/simple-chat/${sessionId}`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Simple chat WebSocket connected');
        setIsConnected(true);
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'agent_message') {
          setMessages(prev => [...prev, {
            type: 'agent_message',
            message: data.message,
            timestamp: data.timestamp
          }]);
          setIsLoading(false);
          
          if (data.is_complete) {
            setIsComplete(true);
          }
        } else if (data.type === 'connection_established') {
          console.log('Connection established for session:', data.session_id);
        }
      };

      wsRef.current.onclose = () => {
        console.log('Simple chat WebSocket disconnected');
        setIsConnected(false);
      };

      wsRef.current.onerror = (error) => {
        console.error('Simple chat WebSocket error:', error);
        setIsConnected(false);
      };
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [sessionId]);

  const sendMessage = () => {
    if (!inputMessage.trim() || !wsRef.current || !isConnected) return;

    // Add user message to chat
    const userMessage: Message = {
      type: 'user_message',
      message: inputMessage,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Send message via WebSocket
    wsRef.current.send(JSON.stringify({
      type: 'user_message',
      message: inputMessage
    }));

    setInputMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleEndSession = () => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    setIsComplete(true);
    onSessionEnd?.();
  };

  return (
    <div className="simple-chat-container">
      <div className="simple-chat-header">
        <h2>Emergency Chat</h2>
        <div className="connection-status">
          <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
            {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </span>
          {sessionId && (
            <span className="session-id">Session: {sessionId.slice(-8)}</span>
          )}
        </div>
      </div>

      <div className="messages-container">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`message ${message.type === 'user_message' ? 'user' : 'agent'}`}
          >
            <div className="message-content">
              <div className="message-text">{message.message}</div>
              <div className="message-timestamp">
                {new Date(message.timestamp).toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="message agent">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <div className="input-container">
        <div className="input-wrapper">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={isComplete ? "Session completed" : "Type your message..."}
            disabled={!isConnected || isComplete}
            rows={1}
          />
          <button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || !isConnected || isComplete}
            className="send-button"
          >
            Send
          </button>
        </div>
        
        {isComplete && (
          <div className="session-controls">
            <button onClick={handleEndSession} className="end-session-button">
              End Session
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleChat;
