["backend/tests/test_integration.py::TestSystemIntegration::test_complete_injury_accident_workflow", "backend/tests/test_integration.py::TestSystemIntegration::test_complete_rsa_workflow", "backend/tests/test_integration.py::TestSystemIntegration::test_conversation_context_preservation", "backend/tests/test_integration.py::TestSystemIntegration::test_llm_service_error_handling", "backend/tests/test_integration.py::TestSystemIntegration::test_metrics_tracking", "backend/tests/test_integration.py::TestSystemIntegration::test_session_id_consistency", "backend/tests/test_integration.py::TestSystemIntegration::test_unclear_situation_clarification", "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_injury_accident", "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_light_accident", "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_road_hazard", "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_rsa_need", "backend/tests/test_llm_service.py::TestLLMService::test_conversation_history_formatting", "backend/tests/test_llm_service.py::TestLLMService::test_fallback_on_api_error", "backend/tests/test_llm_service.py::TestLLMService::test_generate_greeting", "backend/tests/test_llm_service.py::TestLLMService::test_generate_response_with_context", "backend/tests/test_llm_service.py::TestLLMService::test_llm_service_initialization", "backend/tests/test_services.py::TestPSAPService::test_contact_psap_injury_accident", "backend/tests/test_services.py::TestPSAPService::test_contact_psap_light_accident", "backend/tests/test_services.py::TestPSAPService::test_contact_psap_road_hazard", "backend/tests/test_services.py::TestPSAPService::test_get_psap_status", "backend/tests/test_services.py::TestPSAPService::test_psap_llm_fallback", "backend/tests/test_services.py::TestPSAPService::test_psap_response_message_generation", "backend/tests/test_services.py::TestRSAService::test_contact_rsa_dead_battery", "backend/tests/test_services.py::TestRSAService::test_contact_rsa_flat_tire", "backend/tests/test_services.py::TestRSAService::test_contact_rsa_lockout", "backend/tests/test_services.py::TestRSAService::test_contact_rsa_tow_needed", "backend/tests/test_services.py::TestRSAService::test_get_rsa_status", "backend/tests/test_services.py::TestRSAService::test_rsa_llm_fallback", "backend/tests/test_services.py::TestRSAService::test_rsa_service_instructions", "backend/tests/test_services.py::TestRSAService::test_rsa_urgency_handling", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_injury_accident", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_rsa_need", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_unknown", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_conversation_history_preservation", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_end_interaction", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_handle_unknown", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_llm_integration_in_nodes", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_injury_accident", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_light_accident", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_road_hazard", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_rsa_request", "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_start_interaction"]