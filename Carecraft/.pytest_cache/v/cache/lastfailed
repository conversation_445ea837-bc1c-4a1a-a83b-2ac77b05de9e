{"backend/tests/test_integration.py::TestSystemIntegration::test_complete_injury_accident_workflow": true, "backend/tests/test_integration.py::TestSystemIntegration::test_complete_rsa_workflow": true, "backend/tests/test_integration.py::TestSystemIntegration::test_unclear_situation_clarification": true, "backend/tests/test_integration.py::TestSystemIntegration::test_conversation_context_preservation": true, "backend/tests/test_integration.py::TestSystemIntegration::test_llm_service_error_handling": true, "backend/tests/test_integration.py::TestSystemIntegration::test_metrics_tracking": true, "backend/tests/test_integration.py::TestSystemIntegration::test_session_id_consistency": true, "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_injury_accident": true, "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_light_accident": true, "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_rsa_need": true, "backend/tests/test_llm_service.py::TestLLMService::test_classify_incident_road_hazard": true, "backend/tests/test_llm_service.py::TestLLMService::test_generate_response_with_context": true, "backend/tests/test_llm_service.py::TestLLMService::test_generate_greeting": true, "backend/tests/test_llm_service.py::TestLLMService::test_fallback_on_api_error": true, "backend/tests/test_llm_service.py::TestLLMService::test_conversation_history_formatting": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_start_interaction": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_injury_accident": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_rsa_need": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_unknown": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_injury_accident": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_light_accident": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_road_hazard": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_rsa_request": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_handle_unknown": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_end_interaction": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_conversation_history_preservation": true, "backend/tests/test_workflow_nodes.py::TestWorkflowNodes::test_llm_integration_in_nodes": true}