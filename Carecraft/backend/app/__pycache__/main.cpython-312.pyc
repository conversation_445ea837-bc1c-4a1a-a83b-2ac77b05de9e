�

    ��[h.N  �                   �L  � U d Z ddlZddlZddlZddlZddlmZmZm	Z	m
Z
 ddlmZ ddl
mZ ddlmZmZmZmZ ddlZddlmZ ddlZddlmZmZ dd	lmZmZ  ej:                  ej<                  �
�        ej>                  e �      Z! eddd
��      Z"e"jG                  eg d�dg d�dg��       i Z$ee%ef   e&d<    G d� d�      Z' e'�       Z(de)dee%ef   fd�Z*de%de%dee%ef   fd�Z+ G d� de�      Z, G d� d e�      Z- G d!� d"e�      Z.e"j_                  d#�      d$� �       Z0e"j_                  d%�      d&� �       Z1e"je                  d'e.�(�      d)e,fd*��       Z3e"je                  d+e.�(�      de%d)e-fd,��       Z4e"j_                  d-�      de%fd.��       Z5e"jm                  d+�      de%fd/��       Z7e"j_                  d0�      d1� �       Z8e"js                  d2�      d3e	de%fd4��       Z:e"j_                  d5�      d6� �       Z;e"je                  d7�      dee%ef   fd8��       Z<e d9k(  r ejz                  e"d:d;�<�       yy)=z�
FastAPI Server for Bosch eCall Emergency Communication System

This module exposes the LangGraph workflow via REST API endpoints
for handling eCall emergency sessions.
�    N)�FastAPI�
HTTPException�	WebSocket�WebSocketDisconnect)�CORSMiddleware)�	BaseModel)�Dict�Any�Optional�List)�datetime)�run_workflow_step�get_workflow_visualization)�create_initial_state�
AgentState)�level�*Bosch eCall Emergency Communication Systemz>AI-driven emergency communication system for vehicle incidents�1.0.0)�title�description�version)zhttp://localhost:3000zhttp://127.0.0.1:3000zhttp://localhost:3001zhttp://localhost:8080T)�GET�POST�PUT�DELETE�OPTIONS�*)�
allow_origins�allow_credentials�
allow_methods�
allow_headers�sessionsc                   �N   � e Zd Zd� Zdedefd�Zdedefd�Zdedefd�Z	defd�Z
y	)
�ConnectionManagerc                 �    � g | _         i | _        y )N)�active_connections�session_connections)�selfs    �K/Users/<USER>/Documents/bosch_hackathon/Carecraft/backend/app/main.py�__init__zConnectionManager.__init__4   s   � �35���9;�� �    �	websocket�
session_idc              �   ��   K  � |j                  �       � d {  ���  | j                  j                  |�       || j                  |<   t        j                  d|� ��       y 7 �G�w)Nz!WebSocket connected for session: )�acceptr&   �appendr'   �logger�info�r(   r,   r-   s      r)   �connectzConnectionManager.connect8   sU   � �� ���� � � ����&�&�y�1�/8�� � ��,����7�
�|�D�E� 	!�s   �A �A�AA c                 �   � || j                   v r| j                   j                  |�       || j                  v r
| j                  |= t        j	                  d|� ��       y )Nz$WebSocket disconnected for session: )r&   �remover'   r1   r2   r3   s      r)   �
disconnectzConnectionManager.disconnect>   sT   � ���/�/�/��#�#�*�*�9�5���1�1�1��(�(��4����:�:�,�G�Hr+   �messagec              �   �  K  � || j                   v r=| j                   |   }	 |j                  t        j                  |�      �      � d {  ���  y y 7 �# t        $ r%}t
        j
                  d|� d|� ��       Y d }~y d }~ww xY w�w)NzError sending message to �: )r'   �	send_text�json�dumps�	Exceptionr1   �error)r(   r8   r-   r,   �es        r)   �send_personal_messagez'ConnectionManager.send_personal_messageE   s   � �� ���1�1�1��0�0��<�I�
L��)�)�$�*�*�W�*=�>�>�>� 2� ?��� 
L����8���B�q�c�J�K�K��
L�s@   �B�'A �A�	A �
B�A �	A?�A:�5B�:A?�?Bc              �   ��   K  � | j                   D ]/  }	 |j                  t        j                  |�      �      � d {  ���  �1 y 7 �# t        $ r"}t
        j
                  d|� ��       Y d }~�[d }~ww xY w�w)NzError broadcasting message: )r&   r;   r<   r=   r>   r1   r?   )r(   r8   �
connectionr@   s       r)   �	broadcastzConnectionManager.broadcastM   sd   � �� ��1�1�J�
A� �*�*�4�:�:�g�+>�?�?�?� 2�?��� 
A����;�A�3�?�@�@��
A�s=   �A2�'A�A�A�A2�A�	A/�
A*�%A2�*A/�/A2N)�__name__�
__module__�__qualname__r*   r   �strr4   r7   �dictrA   rD   � r+   r)   r$   r$   3   sW   � �<�F�y� F�c� F�I�I� I�3� I�L�4� L�S� L�A�t� Ar+   r$   �
audio_data�returnc                 �  � 	 t        j                  | t         j                  ��      }t        j                  t        j                  |j                  t         j                  �      dz  �      �      }dt        j                  |dz   �      z  }t        dt        d|dz   dz  dz  �      �      }t        |�      dt        |�      d	z  t        |�      d
�S # t        $ r)}t        j                  d|� ��       ddddd
�cY d}~S d}~ww xY w)
z,Process raw audio data and extract features.)�dtype�   �   g�����|�=r   �d   �<   iD�  g�����F@)�volume_level�sample_rate�duration_ms�rmszError processing audio data: N)�np�
frombuffer�int16�sqrt�mean�astype�float32�log10�max�min�float�lenr>   r1   r?   )rK   �audio_array�volume�	volume_db�volume_normalizedr@   s         r)   �process_audio_datarg   W   s�   � �U��m�m�J�b�h�h�?�� �������!3�!3�B�J�J�!?�1�!D�E�F������&�5�.�1�1�	�  ��3�s�Y��^�r�,A�C�,G�#H�I�� "�"3�4� ��{�+�d�2���=�	
� 	
�� � U����4�Q�C�8�9� !�%��RS�T�T��U�s   �CC �	D �C;�5D �;D r-   �user_messagec           	   �   ��  K  � t        j                  d�      � d{  ���  | t        v rjt        |    }||d<   t        |�      }|t        | <   d| |j	                  dd�      |j	                  dd�      |j	                  d	�      |j	                  d
i �      d�S t        | |�      }t        |�      }|t        | <   d| |j	                  dd�      |j	                  dd�      |j	                  d	�      |j	                  d
i �      d�S 7 �ޭw)z-Simulate AI processing and generate response.g      �?N�
user_input�ai_response�final_response� �is_completeF�
incident_type�metrics)�typer-   r8   rn   ro   rp   )�asyncio�sleepr"   r   �getr   )r-   rh   �
current_state�
updated_state�
initial_states        r)   �simulate_ai_responserx   n   s  � �� �
�-�-��
��� �X�� ��,�
�&2�
�l�#�)�-�8�
�,���� "�$�$�(�(�)9�2�>�(�,�,�]�E�B�*�.�.��?�$�(�(��B�7�

� 	
� -�Z��F�
�)�-�8�
�,���� "�$�$�(�(�)9�2�>�(�,�,�]�E�B�*�.�.��?�$�(�(��B�7�

� 	
�/ �s   �C<�C:�CC<c                   �   � e Zd ZU dZdZee   ed<   dZee	ee
f      ed<   dZee	ee
f      ed<   dZee	ee
f      ed<   y)�SessionStartRequestz/Request model for starting a new eCall session.rm   �initial_messageN�vehicle_info�location�
customer_info)
rE   rF   rG   �__doc__r{   r   rH   �__annotations__r|   r	   r
   r}   r~   rJ   r+   r)   rz   rz   �   sX   � �9�%'�O�X�c�]�'�-1�L�(�4��S��>�*�1�)-�H�h�t�C��H�~�&�-�.2�M�8�D��c��N�+�2r+   rz   c                   �   � e Zd ZU dZeed<   y)�SessionMessageRequestz;Request model for sending a message in an existing session.r8   N)rE   rF   rG   r   rH   r�   rJ   r+   r)   r�   r�   �   s
   � �E�
�Lr+   r�   c                   �v   � e Zd ZU dZeed<   eed<   eed<   dZee   ed<   dZ	ee   ed<   dZ
eeeef      ed<   y)	�SessionResponsez.Response model for eCall session interactions.r-   r8   rn   Nro   �next_actionrp   )
rE   rF   rG   r   rH   r�   �boolro   r   r�   rp   r	   r
   rJ   r+   r)   r�   r�   �   sH   � �8��O�
�L���#'�M�8�C�=�'�!%�K��#��%�(,�G�X�d�3��8�n�
%�,r+   r�   �/c                  �P   � dddt        j                  �       j                  �       d�S )zHealth check endpoint.�healthyr   r   )�status�servicer   �	timestamp)r
   �now�	isoformatrJ   r+   r)   �health_checkr�   �   s)   � � �?���\�\�^�-�-�/�	� r+   z/workflow/visualizationc                  �&   � t        �       g d�g d�d�S )z-Get information about the workflow structure.)�INJURY_ACCIDENT�LIGHT_ACCIDENT�RSA_NEED�ROAD_HAZARD�UNKNOWN)�ASK_ABOUT_INJURIES�CONFIRM_LOCATION�CONTACT_PSAP�CONTACT_RSA�ASK_FOR_CLARIFICATION�END_INTERACTION)�workflow_structure�supported_incident_types�available_actions)r   rJ   r+   r)   �get_workflow_infor�   �   s   � � 9�:�%
�
�� r+   z/e-call/session)�response_model�requestc           
      �R  � 	 t        t        j                  �       �      }t        || j                  �      }i }| j
                  r| j
                  |d<   | j                  r| j                  |d<   | j                  r| j                  |d<   ||d<   t        |�      }|t        |<   t        ||j                  dd�      |j                  dd�      |j                  d	�      |j                  d
�      |j                  d�      ��      S # t        $ r}t        d
dt        |�      � ���      �d}~ww xY w)a  
    Initialize a new eCall session and return the agent's first message.

    This endpoint creates a new emergency communication session, sets up
    the initial state with any provided context, and returns the agent's
    greeting message along with a unique session ID.
    r|   r}   r~   �contextrl   rm   rn   Fro   r�   rp   �r-   r8   rn   ro   r�   rp   ��  zFailed to start eCall session: ��status_code�detailN)rH   �uuid�uuid4r   r{   r|   r}   r~   r   r"   r�   rt   r>   r   )r�   r-   rw   r�   rv   r@   s         r)   �start_ecall_sessionr�   �   s,  � �&
������&�
� -�Z��9P�9P�Q�
� �����&-�&:�&:�G�N�#����")�"2�"2�G�J��� � �'.�'<�'<�G�O�$�#*�
�i� � *�-�8�
�  -���� �!�!�%�%�&6��;�%�)�)�-��?�'�+�+�O�<�%�)�)�-�8�!�%�%�i�0�

� 	
�� � 
���4�S��V�H�=�
� 	
��
�s   �C<C? �?	D&�D!�!D&z/e-call/session/{session_id}c           
      �P  � 	 | t         vrt        dd| � d���      �t         |    }|j                  dd�      r>t        | dd|j                  d	�      |j                  d
�      |j                  d�      ��      S |j                  |d
<   t        |�      }|t         | <   t        | |j                  dd�      |j                  dd�      |j                  d	�      |j                  d
�      |j                  d�      ��      S # t        $ r � t        $ r}t        ddt        |�      � ���      �d}~ww xY w)z�
    Continue an existing eCall session with a user message.

    This endpoint takes the user's reply, loads the state for the session,
    runs it through the LangGraph workflow, saves the new state, and
    returns the agent's response.
    �  �Session �
 not foundr�   rn   FzqThis emergency session has already been completed. If you need additional assistance, please start a new session.Tro   r�   rp   r�   rj   rl   rm   r�   zFailed to process message: N)r"   r   rt   r�   r8   r   r>   rH   )r-   r�   ru   rv   r@   s        r)   �continue_ecall_sessionr�   	  sL  � �/
��X�%���!�*��Z�8�� 
� !��,�
� ���]�E�2�"�%� L� �+�/�/��@�)�-�-�m�<�%�)�)�)�4�
� 
� '.�o�o�
�l�#� *�-�8�
�  -���� �!�!�%�%�&6��;�%�)�)�-��?�'�+�+�O�<�%�)�)�-�8�!�%�%�i�0�

� 	
�� � �
�� 
���0��Q���9�
� 	
��
�s   �A1C5 �4B C5 �5D%�D � D%z#/e-call/session/{session_id}/statusc           
      �  � 	 | t         vrt        dd| � d���      �t         |    }| |j                  dd�      |j                  d�      |j                  d�      t        |j                  d	g �      �      |j                  d
i �      t	        j
                  �       j
                  �       d�S # t        $ r � t        $ r}t        dd
t        |�      � ���      �d}~ww xY w)z�
    Get the current status of an eCall session.

    This endpoint returns the current state information for a session
    including conversation history, metrics, and completion status.
    r�   r�   r�   r�   rn   Fro   r�   �historyrp   )r-   rn   ro   r�   �conversation_lengthrp   �last_updatedr�   zFailed to get session status: N�	r"   r   rt   rb   r
   r�   r�   r>   rH   )r-   ru   r@   s      r)   �get_session_statusr�   E  s�   � �
��X�%���!�*��Z�8�� 
�
 !��,�
� %�(�,�,�]�E�B�*�.�.��?�(�,�,�]�;�#&�}�'8�'8��B�'G�#H�$�(�(��B�7�$�L�L�N�4�4�6�
� 	
�� � �
�� 
���3�C��F�8�<�
� 	
��
�s   �B"B% �%C�7C�Cc           	      �  � 	 | t         vrt        dd| � d���      �t         |    }t         | = d| |j                  d�      t        |j                  dg �      �      |j                  dd	�      |j                  d
i �      t	        j
                  �       j
                  �       d�S # t        $ r � t        $ r}t        dd
t        |�      � ���      �d}~ww xY w)z�
    End and clean up an eCall session.

    This endpoint removes the session from memory and returns
    a summary of the session.
    r�   r�   r�   r�   zSession ended successfullyro   r�   rn   Frp   )r8   r-   �final_incident_typer�   �
was_completedrp   �ended_atr�   zFailed to end session: Nr�   )r-   �session_datar@   s      r)   �end_sessionr�   k  s�   � �
��X�%���!�*��Z�8�� 
�  �
�+�� 
�Z� � 4�$�#/�#3�#3�O�#D�#&�|�'7�'7�	�2�'F�#G�)�-�-�m�U�C�#�'�'�	�2�6� ����0�0�2�
� 	
�� � �
�� 
���,�S��V�H�5�
� 	
��
�s   �BB �C
�/C�C
z/e-call/sessionsc                  �  � 	 g } t         j                  �       D ]�  \  }}| j                  ||j                  d�      |j                  dd�      t	        |j                  dg �      �      |j                  di �      j                  d�      t        j                  �       j                  �       d��       �� t	        t         �      | t        j                  �       j                  �       d�S # t        $ r}t        d	d
t        |�      � ���      �d}~ww xY w)
z�
    List all currently active eCall sessions.

    This endpoint returns a summary of all sessions currently in memory.
    Useful for monitoring and debugging purposes.
    ro   rn   Fr�   rp   �call_start_time)r-   ro   rn   r�   �
start_time�
last_activity)�active_sessionsr"   r�   r�   zFailed to list sessions: r�   N)r"   �itemsr0   rt   rb   r
   r�   r�   r>   r   rH   )�session_summariesr-   �stater@   s       r)   �list_active_sessionsr�   �  s�   � �
���!)���!1��J���$�$�(�!&���?�!;�$�y�y���>�'*�5�9�9�Y��+C�'D�#�i�i�	�2�6�:�:�;L�M�!)����!9�!9�!;�
&� 
� "2�  #�8�}�)�!����1�1�3�
� 	
�� � 
���.�s�1�v�h�7�
� 	
��
�s   �CC" �"	D	�+D�D	z/ws/{session_id}r,   c           	   �   ��	  K  � t         j                  | |�      � d{  ���  t        | j                  j                  d�      si | j                  j                  _        | j                  j                  j
                  }||vr
t
        �       ||<   ddlm}m	} 	 | j                  t        j                  d|t        j                  �       j                  �       d��      �      � d{  ���   |�       }	 | j!                  �       � d{  ��� }t        j"                  |�      }|d   dk(  r�t$        j'                  |d	   �      }||xx   |z
  cc<   t)        |�      }	| j                  t        j                  d
||	t        j                  �       j                  �       d��      �      � d{  ���  �n�|d   dk(  �r�|j+                  |d
�      }
|
sS| j                  t        j                  d|dt        j                  �       j                  �       d��      �      � d{  ���  ��)ddl}ddl}|j1                  �       }
|j3                  |
d�      5 }|j5                  |j6                  �       |j9                  |j:                  j=                  |j>                  �      �       |jA                  |jB                  �       |jE                  |
�       ddd�       |
jG                  d�       d|
_$         ||
�      }| j                  t        j                  d||t        j                  �       j                  �       d��      �      � d{  ���  t
        �       ||<   �n|d   dk(  rF|d   }tK        ||�      � d{  ��� }| j                  t        j                  |�      �      � d{  ���  n�|d   dk(  rP| j                  t        j                  dt        j                  �       j                  �       d��      �      � d{  ���  n^|d   dk(  rV|d   }| j                  t        j                  d|dt        j                  �       j                  �       d��      �      � d{  ���  ��\7 ��.7 ��l7 ��O7 ���7 ��G# 1 sw Y   ���xY w7 ��67 ��	7 ��7 ��7 �,# tL        $ r2 t         jO                  | |�       tP        jS                  d|� d ��       Y ytT        $ r;}tP        jW                  d!|� d"|� ��       t         jO                  | |�       Y d}~yd}~ww xY w�w)#z/WebSocket endpoint for real-time communication.N�pcm_buffersr   )�AudioHandler�speech_to_text�connection_established)rq   r-   r�   rq   rK   �data�audio_analysis)rq   r-   �featuresr�   �
audio_stopr+   �audio_errorzNo audio data buffered.)rq   r-   r?   r�   �wbzstreamed_audio.wav�audio_transcript)rq   r-   �
transcriptr�   �text_messager8   �ping�pong)rq   r�   �
voice_command�command�command_acknowledgment�received)rq   r�   r�   r�   zClient z
 disconnectedzWebSocket error for session r:   ),�managerr4   �hasattr�appr�   r�   �	bytearray�app.rsa_example_workflowr�   r�   r;   r<   r=   r
   r�   r�   �receive_text�loads�bytes�fromhexrg   rt   �io�wave�BytesIO�open�setnchannels�channels�setsampwidth�audio�get_sample_size�format�setframeraterT   �writeframes�seek�namerx   r   r7   r1   r2   r>   r?   )r,   r-   r�   r�   r�   �
audio_handlerr�   r8   �audio_bytes�audio_features�pcm_datar�   r�   �
wav_buffer�wav_filer�   rh   rk   r�   r@   s                       r)   �websocket_endpointr�   �  s\  � �� � �/�/�)�Z�
0�0�0� �9�=�=�&�&�
�6�*,�	�
�
���'��-�-�%�%�1�1�K���$�"+�+��J�� F�_2��!�!�$�*�*�,�$�!����1�1�3�.
� #� � 	� 	� %��
��"�/�/�1�1�D��j�j��&�G��v��,�.�#�m�m�G�F�O�<���J�'�;�6�'� "4�K�!@���)�)�$�*�*�,�",� .�!)����!9�!9�!;�	6� +� � � � ���L�0�&�?�?�:�s�;���#�-�-�d�j�j� -�&0�!:�%-�\�\�^�%=�%=�%?�	:� /� � � � ����Z�Z�\�
��Y�Y�z�4�0�H��)�)�-�*@�*@�A��)�)�-�*=�*=�*M�*M�m�Nb�Nb�*c�d��)�)�-�*C�*C�D��(�(��2�	 1�
 ����"�"6�
�� ,�J�7�
��)�)�$�*�*�.�",�",�!)����!9�!9�!;�	6� +� � � � +4�+��J�'����N�2�&�y�1��$8��\�$R�R��  �)�)�$�*�*�[�*A�B�B�B����F�*��)�)�$�*�*�"�!)����!9�!9�!;�6� +� � � �
 ���O�3�!�)�,���)�)�$�*�*�4�&�(�!)����!9�!9�!;�	6� +� � � �S �- 1��	�� 2������ 1�0���� S�� C������ � 9����9�j�1����g�j�\��7�8�� 2����3�J�<�r�!��E�F����9�j�1�1��2�s  �S2�Q
�A8S2�AQ2 �!Q
�"Q2 �Q�BQ2 �Q� A/Q2 �Q�0Q2 � A<Q�<A4Q2 �0Q&�1/Q2 � Q)�!+Q2 �Q,�
AQ2 �$Q.�%AQ2 �Q0�Q2 �
S2�
Q2 �Q2 �Q2 �Q2 �Q#�	Q2 �)Q2 �,Q2 �.Q2 �0Q2 �28S/�*S2�,S/�41S*�%S2�*S/�/S2z
/ws/statusc                  ��   � t        t        j                  �      t        t        j                  j                  �       �      t
        j                  �       j                  �       d�S )z Get WebSocket connection status.)r&   r'   r�   )	rb   r�   r&   �listr'   �keysr
   r�   r�   rJ   r+   r)   �websocket_statusr�   )  sE   � � "�'�"<�"<�=�#�G�$?�$?�$D�$D�$F�G��\�\�^�-�-�/�� r+   z/audio/processc              �   �  K  � 	 d| vr
t        dd��      �t        j                  | d   �      }t        |�      }d|t	        j
                  �       j
                  �       d�S # t        $ r}t        ddt        |�      � ���      �d	}~ww xY w�w)
z.REST endpoint for audio processing (fallback).r�   i�  zAudio data is requiredr�   �success)r�   r�   r�   r�   zAudio processing failed: N)	r   r�   r�   rg   r
   r�   r�   r>   rH   )rK   r�   r�   r@   s       r)   �process_audio_endpointr�   3  s�   � �� �
Z���#��C�8P�Q�Q��m�m�J�v�$6�7��%�k�2��  � �!����1�1�3�
� 	
��
 � Z���6O�PS�TU�PV�x�4X�Y�Y��Z�s)   �B�AA �B�	B�'B � B�B�__main__z0.0.0.0i@  )�host�port)>r   �uvicornr<   rr   �numpyrW   �fastapir   r   r   r   �fastapi.middleware.corsr   �pydanticr   �typingr	   r
   r   r   r�   r
   �logging�app.agent.workflowr   r   �app.agent.stater   r   �basicConfig�INFO�	getLoggerrE   r1   r�   �add_middlewarer"   rH   r�   r$   r�   r�   rg   rx   rz   r�   r�   rt   r�   r�   �postr�   r�   r�   �deleter�   r�   r,   r�   r�   r�   �runrJ   r+   r)   �<module>r     s�  ��� � � � � J� J� 2� � ,� ,� � � � L� <� �� � �'�,�,� '�	��	�	�8�	$��
�
6�P���� � � ��� �=��%� � � #%��$�s�J��
� $�A� A�B �
��U�5� U�T�#�s�(�^� U�. 
�3�  
�c�  
�d�3�PS�8�n�  
�F3�)� 3��I� �
-�i� -� ������ �� ���	"�#�� $��2 ���
�O��<�/
�!4� /
� =�/
�p ���
(���I�8
�s� 8
�5J� 8
� J�8
�v ���	.�/�"
�3� "
� 0�"
�J ���*�+�&
�C� &
� ,�&
�R ���	��
� �
�D ���!�"�m2�	� m2�s� m2� #�m2�` ������ �� ���
��Z�T�#�s�(�^� Z� �Z�$ �z���G�K�K��)�$�/� r+   