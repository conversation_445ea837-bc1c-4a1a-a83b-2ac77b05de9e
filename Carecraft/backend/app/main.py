"""
FastAPI Server for Bosch eCall Emergency Communication System

This module exposes the LangGraph workflow via REST API endpoints
for handling eCall emergency sessions.
"""

import uvicorn
import json
import asyncio
import numpy as np
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uuid
from datetime import datetime
import logging

from app.agent.workflow import run_workflow_step, get_workflow_visualization
from app.agent.state import create_initial_state, AgentState

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Bosch eCall Emergency Communication System",
    description="AI-driven emergency communication system for vehicle incidents",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000",
        "http://localhost:3001",  # Alternative React port
        "http://localhost:8080",  # Alternative frontend port
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# In-memory session storage (in production, use Redis or database)
sessions: Dict[str, AgentState] = {}

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.session_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.session_connections[session_id] = websocket
        logger.info(f"WebSocket connected for session: {session_id}")

    def disconnect(self, websocket: WebSocket, session_id: str):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if session_id in self.session_connections:
            del self.session_connections[session_id]
        logger.info(f"WebSocket disconnected for session: {session_id}")

    async def send_personal_message(self, message: dict, session_id: str):
        if session_id in self.session_connections:
            websocket = self.session_connections[session_id]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {session_id}: {e}")

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")

manager = ConnectionManager()

# Audio processing utilities
def process_audio_data(audio_data: bytes) -> Dict[str, Any]:
    """Process raw audio data and extract features."""
    try:
        # Convert bytes to numpy array (assuming 16-bit PCM)
        audio_array = np.frombuffer(audio_data, dtype=np.int16)
        
        # Calculate volume level
        volume = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
        volume_db = 20 * np.log10(volume + 1e-10)  # Add small value to avoid log(0)
        
        # Normalize volume to 0-100 scale
        volume_normalized = max(0, min(100, (volume_db + 60) / 60 * 100))
        
        return {
            "volume_level": float(volume_normalized),
            "sample_rate": 44100,  # Assumed sample rate
            "duration_ms": len(audio_array) / 44.1,  # Duration in milliseconds
            "rms": float(volume)
        }
    except Exception as e:
        logger.error(f"Error processing audio data: {e}")
        return {"volume_level": 0, "sample_rate": 44100, "duration_ms": 0, "rms": 0}

async def simulate_ai_response(session_id: str, user_message: str) -> Dict[str, Any]:
    """Simulate AI processing and generate response."""
    await asyncio.sleep(0.5)  # Simulate processing time
    
    # Use existing workflow if session exists
    if session_id in sessions:
        current_state = sessions[session_id]
        current_state["user_input"] = user_message
        updated_state = run_workflow_step(current_state)
        sessions[session_id] = updated_state
        
        return {
            "type": "ai_response",
            "session_id": session_id,
            "message": updated_state.get("final_response", ""),
            "is_complete": updated_state.get("is_complete", False),
            "incident_type": updated_state.get("incident_type"),
            "metrics": updated_state.get("metrics", {})
        }
    else:
        # Create new session if needed
        initial_state = create_initial_state(session_id, user_message)
        updated_state = run_workflow_step(initial_state)
        sessions[session_id] = updated_state
        
        return {
            "type": "ai_response",
            "session_id": session_id,
            "message": updated_state.get("final_response", ""),
            "is_complete": updated_state.get("is_complete", False),
            "incident_type": updated_state.get("incident_type"),
            "metrics": updated_state.get("metrics", {})
        }


class SessionStartRequest(BaseModel):
    """Request model for starting a new eCall session."""
    initial_message: Optional[str] = ""
    vehicle_info: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, Any]] = None
    customer_info: Optional[Dict[str, Any]] = None


class SessionMessageRequest(BaseModel):
    """Request model for sending a message in an existing session."""
    message: str


class SessionResponse(BaseModel):
    """Response model for eCall session interactions."""
    session_id: str
    message: str
    is_complete: bool
    incident_type: Optional[str] = None
    next_action: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None





@app.get("/")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "Bosch eCall Emergency Communication System",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/workflow/visualization")
def get_workflow_info():
    """Get information about the workflow structure."""
    return {
        "workflow_structure": get_workflow_visualization(),
        "supported_incident_types": [
            "INJURY_ACCIDENT",
            "LIGHT_ACCIDENT",
            "RSA_NEED",
            "ROAD_HAZARD",
            "UNKNOWN"
        ],
        "available_actions": [
            "ASK_ABOUT_INJURIES",
            "CONFIRM_LOCATION",
            "CONTACT_PSAP",
            "CONTACT_RSA",
            "ASK_FOR_CLARIFICATION",
            "END_INTERACTION"
        ]
    }





@app.post("/e-call/session", response_model=SessionResponse)
def start_ecall_session(request: SessionStartRequest):
    """
    Initialize a new eCall session and return the agent's first message.

    This endpoint creates a new emergency communication session, sets up
    the initial state with any provided context, and returns the agent's
    greeting message along with a unique session ID.
    """

    try:
        # Generate unique session ID
        session_id = str(uuid.uuid4())

        # Create initial state
        initial_state = create_initial_state(session_id, request.initial_message)

        # Add context information if provided
        context = {}
        if request.vehicle_info:
            context["vehicle_info"] = request.vehicle_info
        if request.location:
            context["location"] = request.location
        if request.customer_info:
            context["customer_info"] = request.customer_info

        initial_state["context"] = context

        # Run the workflow to get the initial response
        updated_state = run_workflow_step(initial_state)

        # Store the session state
        sessions[session_id] = updated_state

        # Return the response
        return SessionResponse(
            session_id=session_id,
            message=updated_state.get("final_response", ""),
            is_complete=updated_state.get("is_complete", False),
            incident_type=updated_state.get("incident_type"),
            next_action=updated_state.get("next_action"),
            metrics=updated_state.get("metrics")
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start eCall session: {str(e)}"
        )








@app.post("/e-call/session/{session_id}", response_model=SessionResponse)
def continue_ecall_session(session_id: str, request: SessionMessageRequest):
    """
    Continue an existing eCall session with a user message.

    This endpoint takes the user's reply, loads the state for the session,
    runs it through the LangGraph workflow, saves the new state, and
    returns the agent's response.
    """

    try:
        # Check if session exists
        if session_id not in sessions:
            raise HTTPException(
                status_code=404,
                detail=f"Session {session_id} not found"
            )

        # Get current session state
        current_state = sessions[session_id]

        # Check if session is already complete
        if current_state.get("is_complete", False):
            return SessionResponse(
                session_id=session_id,
                message="This emergency session has already been completed. If you need additional assistance, please start a new session.",
                is_complete=True,
                incident_type=current_state.get("incident_type"),
                next_action=current_state.get("next_action"),
                metrics=current_state.get("metrics")
            )

        # Update state with new user input
        current_state["user_input"] = request.message

        # Run the workflow with updated state
        updated_state = run_workflow_step(current_state)

        # Update the stored session state
        sessions[session_id] = updated_state

        # Return the response
        return SessionResponse(
            session_id=session_id,
            message=updated_state.get("final_response", ""),
            is_complete=updated_state.get("is_complete", False),
            incident_type=updated_state.get("incident_type"),
            next_action=updated_state.get("next_action"),
            metrics=updated_state.get("metrics")
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process message: {str(e)}"
        )


@app.get("/e-call/session/{session_id}/status")
def get_session_status(session_id: str):
    """
    Get the current status of an eCall session.

    This endpoint returns the current state information for a session
    including conversation history, metrics, and completion status.
    """

    try:
        # Check if session exists
        if session_id not in sessions:
            raise HTTPException(
                status_code=404,
                detail=f"Session {session_id} not found"
            )

        current_state = sessions[session_id]

        return {
            "session_id": session_id,
            "is_complete": current_state.get("is_complete", False),
            "incident_type": current_state.get("incident_type"),
            "next_action": current_state.get("next_action"),
            "conversation_length": len(current_state.get("history", [])),
            "metrics": current_state.get("metrics", {}),
            "last_updated": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get session status: {str(e)}"
        )


@app.delete("/e-call/session/{session_id}")
def end_session(session_id: str):
    """
    End and clean up an eCall session.

    This endpoint removes the session from memory and returns
    a summary of the session.
    """

    try:
        # Check if session exists
        if session_id not in sessions:
            raise HTTPException(
                status_code=404,
                detail=f"Session {session_id} not found"
            )

        # Get session data before deletion
        session_data = sessions[session_id]

        # Remove session from memory
        del sessions[session_id]

        return {
            "message": "Session ended successfully",
            "session_id": session_id,
            "final_incident_type": session_data.get("incident_type"),
            "conversation_length": len(session_data.get("history", [])),
            "was_completed": session_data.get("is_complete", False),
            "metrics": session_data.get("metrics", {}),
            "ended_at": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to end session: {str(e)}"
        )


@app.get("/e-call/sessions")
def list_active_sessions():
    """
    List all currently active eCall sessions.

    This endpoint returns a summary of all sessions currently in memory.
    Useful for monitoring and debugging purposes.
    """

    try:
        session_summaries = []

        for session_id, state in sessions.items():
            session_summaries.append({
                "session_id": session_id,
                "incident_type": state.get("incident_type"),
                "is_complete": state.get("is_complete", False),
                "conversation_length": len(state.get("history", [])),
                "start_time": state.get("metrics", {}).get("call_start_time"),
                "last_activity": datetime.now().isoformat()  # Simplified for demo
            })

        return {
            "active_sessions": len(sessions),
            "sessions": session_summaries,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list sessions: {str(e)}"
        )


@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time communication."""
    await manager.connect(websocket, session_id)

    # --- Audio buffer per session ---
    if not hasattr(websocket.app.state, "pcm_buffers"):
        websocket.app.state.pcm_buffers = {}
    pcm_buffers = websocket.app.state.pcm_buffers
    if session_id not in pcm_buffers:
        pcm_buffers[session_id] = bytearray()

    # Import AudioHandler and speech_to_text for WAV processing
    from app.rsa_example_workflow import AudioHandler, speech_to_text

    try:
        # Send initial connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        }))

        audio_handler = AudioHandler()

        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)

            if message["type"] == "audio_data":
                # Buffer PCM audio data
                audio_bytes = bytes.fromhex(message["data"])
                pcm_buffers[session_id] += audio_bytes

                # Optionally, analyze chunk for volume feedback
                audio_features = process_audio_data(audio_bytes)
                await websocket.send_text(json.dumps({
                    "type": "audio_analysis",
                    "session_id": session_id,
                    "features": audio_features,
                    "timestamp": datetime.now().isoformat()
                }))

            elif message["type"] == "audio_stop":
                # Assemble buffered PCM into WAV and process
                pcm_data = pcm_buffers.get(session_id, b"")
                if not pcm_data:
                    await websocket.send_text(json.dumps({
                        "type": "audio_error",
                        "session_id": session_id,
                        "error": "No audio data buffered.",
                        "timestamp": datetime.now().isoformat()
                    }))
                    continue

                import io
                import wave

                wav_buffer = io.BytesIO()
                with wave.open(wav_buffer, 'wb') as wav_file:
                    wav_file.setnchannels(audio_handler.channels)
                    wav_file.setsampwidth(audio_handler.audio.get_sample_size(audio_handler.format))
                    wav_file.setframerate(audio_handler.sample_rate)
                    wav_file.writeframes(pcm_data)
                wav_buffer.seek(0)
                wav_buffer.name = "streamed_audio.wav"

                # Speech-to-text (or other processing)
                transcript = speech_to_text(wav_buffer)
                await websocket.send_text(json.dumps({
                    "type": "audio_transcript",
                    "session_id": session_id,
                    "transcript": transcript,
                    "timestamp": datetime.now().isoformat()
                }))

                # Clear buffer for session
                pcm_buffers[session_id] = bytearray()

            elif message["type"] == "text_message":
                # Process text message through AI workflow
                user_message = message["message"]
                ai_response = await simulate_ai_response(session_id, user_message)

                # Send AI response back to client
                await websocket.send_text(json.dumps(ai_response))

            elif message["type"] == "ping":
                # Respond to ping with pong for heartbeat
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }))

            elif message["type"] == "voice_command":
                # Handle voice commands
                command = message["command"]
                await websocket.send_text(json.dumps({
                    "type": "command_acknowledgment",
                    "command": command,
                    "status": "received",
                    "timestamp": datetime.now().isoformat()
                }))

    except WebSocketDisconnect:
        manager.disconnect(websocket, session_id)
        logger.info(f"Client {session_id} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
        manager.disconnect(websocket, session_id)


@app.websocket("/simple-chat/{session_id}")
async def simple_chat_websocket(websocket: WebSocket, session_id: str):
    """Simple WebSocket endpoint for conversation-only interface."""
    await manager.connect(websocket, session_id)

    try:
        # Send initial connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        }))

        # Send initial greeting if this is a new session
        if session_id not in sessions:
            initial_state = create_initial_state(session_id, "")
            updated_state = run_workflow_step(initial_state)
            sessions[session_id] = updated_state

            # Send initial greeting
            await websocket.send_text(json.dumps({
                "type": "agent_message",
                "session_id": session_id,
                "message": updated_state.get("final_response", ""),
                "timestamp": datetime.now().isoformat()
            }))

        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)

            if message["type"] == "user_message":
                # Process user message through AI workflow
                user_message = message["message"]

                # Get current state and process message
                if session_id in sessions:
                    current_state = sessions[session_id]
                    current_state["user_input"] = user_message
                    updated_state = run_workflow_step(current_state)
                    sessions[session_id] = updated_state

                    # Send only the agent's response
                    await websocket.send_text(json.dumps({
                        "type": "agent_message",
                        "session_id": session_id,
                        "message": updated_state.get("final_response", ""),
                        "is_complete": updated_state.get("is_complete", False),
                        "timestamp": datetime.now().isoformat()
                    }))

            elif message["type"] == "ping":
                # Respond to ping with pong for heartbeat
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }))

    except WebSocketDisconnect:
        manager.disconnect(websocket, session_id)
        logger.info(f"Simple chat client {session_id} disconnected")
    except Exception as e:
        logger.error(f"Simple chat WebSocket error for session {session_id}: {e}")
        manager.disconnect(websocket, session_id)


@app.get("/simple-chat/{session_id}/history")
def get_simple_chat_history(session_id: str):
    """Get conversation history for simple chat interface."""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    state = sessions[session_id]
    history = state.get("history", [])

    # Format history for simple chat interface
    conversation = []
    for human_msg, ai_msg in history:
        if human_msg:  # Only add if there's a user message
            conversation.append({
                "type": "user_message",
                "message": human_msg,
                "timestamp": datetime.now().isoformat()
            })
        if ai_msg:  # Only add if there's an agent message
            conversation.append({
                "type": "agent_message",
                "message": ai_msg,
                "timestamp": datetime.now().isoformat()
            })

    return {
        "session_id": session_id,
        "conversation": conversation,
        "is_complete": state.get("is_complete", False)
    }


@app.get("/ws/status")
def websocket_status():
    """Get WebSocket connection status."""
    return {
        "active_connections": len(manager.active_connections),
        "session_connections": list(manager.session_connections.keys()),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/audio/process")
async def process_audio_endpoint(audio_data: Dict[str, Any]):
    """REST endpoint for audio processing (fallback)."""
    try:
        if "data" not in audio_data:
            raise HTTPException(status_code=400, detail="Audio data is required")
            
        audio_bytes = bytes.fromhex(audio_data["data"])
        features = process_audio_data(audio_bytes)
        
        return {
            "status": "success",
            "features": features,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Audio processing failed: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)