#!/usr/bin/env python3
"""
Bosch eCall Emergency Communication System - Demonstration Script

This script simulates user interactions with the FastAPI server to demonstrate
the system's capabilities in handling different emergency scenarios.

Prerequisites:
- The FastAPI server must be running on http://127.0.0.1:8000
- Start server with: python3 -m uvicorn app.main:app --reload

Author: Bosch eCall Development Team
"""

import requests
import json
import time
from datetime import datetime


# Configuration
BASE_URL = "http://127.0.0.1:8001"
HEADERS = {"Content-Type": "application/json"}


def print_separator(title=""):
    """Print a formatted separator for better readability."""
    if title:
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    else:
        print("-" * 60)


def print_message(speaker, message, timestamp=True):
    """Print a formatted conversation message."""
    if timestamp:
        time_str = datetime.now().strftime("%H:%M:%S")
        print(f"[{time_str}] {speaker}: {message}")
    else:
        print(f"{speaker}: {message}")


def check_server_health():
    """Check if the FastAPI server is running and accessible."""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"❌ Server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        print(f"   Make sure the server is running on {BASE_URL}")
        print("   Start with: python3 -m uvicorn app.main:app --reload")
        return False


def start_session(initial_message="", vehicle_info=None, location=None):
    """
    Start a new eCall session.
    
    Args:
        initial_message: Optional initial message from user
        vehicle_info: Optional vehicle information
        location: Optional location data
    
    Returns:
        tuple: (session_id, agent_response) or (None, None) if failed
    """
    payload = {
        "initial_message": initial_message
    }
    
    if vehicle_info:
        payload["vehicle_info"] = vehicle_info
    
    if location:
        payload["location"] = location
    
    try:
        response = requests.post(
            f"{BASE_URL}/e-call/session",
            headers=HEADERS,
            data=json.dumps(payload),
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("session_id"), data.get("message")
        else:
            print(f"❌ Failed to start session: {response.status_code}")
            print(f"   Response: {response.text}")
            return None, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error starting session: {e}")
        return None, None


def send_message(session_id, message):
    """
    Send a message to an existing session.
    
    Args:
        session_id: The session identifier
        message: User message to send
    
    Returns:
        tuple: (agent_response, is_complete) or (None, True) if failed
    """
    payload = {"message": message}
    
    try:
        response = requests.post(
            f"{BASE_URL}/e-call/session/{session_id}",
            headers=HEADERS,
            data=json.dumps(payload),
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("message"), data.get("is_complete", False)
        else:
            print(f"❌ Failed to send message: {response.status_code}")
            print(f"   Response: {response.text}")
            return None, True
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error sending message: {e}")
        return None, True


def run_injury_accident_scenario():
    """
    Simulate Scenario 1: Injury Accident (High Urgency)
    
    This scenario demonstrates the system's ability to handle a critical event,
    contact PSAP (emergency services), and provide reassurance to the user.
    """
    print_separator("SCENARIO 1: INJURY ACCIDENT (HIGH URGENCY)")
    
    print("🚑 Simulating a critical emergency situation...")
    print("   Vehicle has detected an accident with potential injuries")
    print("   System should classify as INJURY_ACCIDENT and contact PSAP immediately")
    print()
    
    # Add some vehicle context for realism
    vehicle_info = {
        "make": "BMW",
        "model": "X5",
        "year": "2020",
        "license_plate": "ABC-123",
        "vin": "1HGBH41JXMN109186"
    }
    
    location = {
        "latitude": 37.7749,
        "longitude": -122.4194,
        "address": "Highway 101, Mile Marker 45, San Francisco, CA"
    }
    
    # Step 1: Start the session
    print("📞 Initiating emergency communication session...")
    session_id, agent_greeting = start_session(
        initial_message="",
        vehicle_info=vehicle_info,
        location=location
    )
    
    if not session_id:
        print("❌ Failed to start session. Aborting scenario.")
        return
    
    print(f"✅ Session started: {session_id}")
    print()
    
    # Step 2: Agent greeting
    print_message("AGENT", agent_greeting)
    print()
    
    # Step 3: User reports the emergency
    user_message_1 = "There's been a terrible crash! I think someone is hurt!"
    print_message("USER", user_message_1)
    
    agent_response_1, is_complete_1 = send_message(session_id, user_message_1)
    if agent_response_1:
        print_message("AGENT", agent_response_1)
        print()
    
    # Step 4: User asks for confirmation
    if not is_complete_1:
        user_message_2 = "Okay, thank you! Is help on the way?"
        print_message("USER", user_message_2)
        
        agent_response_2, is_complete_2 = send_message(session_id, user_message_2)
        if agent_response_2:
            print_message("AGENT", agent_response_2)
            print()
    
    # Scenario summary
    print_separator()
    print("📊 SCENARIO 1 SUMMARY:")
    print("   ✅ Emergency detected and classified as INJURY_ACCIDENT")
    print("   ✅ PSAP (Emergency Services) contacted immediately")
    print("   ✅ User provided with reference number and instructions")
    print("   ✅ High-priority response initiated (8-12 minute ETA)")
    print_separator()


def run_rsa_scenario():
    """
    Simulate Scenario 2: Roadside Assistance (Standard Urgency)
    
    This scenario demonstrates routing to a non-emergency service (RSA)
    for vehicle breakdown situations.
    """
    print_separator("SCENARIO 2: ROADSIDE ASSISTANCE (STANDARD URGENCY)")
    
    print("🔧 Simulating a vehicle breakdown situation...")
    print("   Vehicle has a mechanical issue requiring roadside assistance")
    print("   System should classify as RSA_NEED and contact service provider")
    print()
    
    # Add vehicle context
    vehicle_info = {
        "make": "Honda",
        "model": "Civic",
        "year": "2019",
        "license_plate": "XYZ-789",
        "vin": "2HGFC2F59KH542318"
    }
    
    location = {
        "latitude": 40.7128,
        "longitude": -74.0060,
        "address": "Main Street Parking Lot, New York, NY"
    }
    
    # Step 1: Start the session
    print("📞 Initiating roadside assistance session...")
    session_id, agent_greeting = start_session(
        initial_message="",
        vehicle_info=vehicle_info,
        location=location
    )
    
    if not session_id:
        print("❌ Failed to start session. Aborting scenario.")
        return
    
    print(f"✅ Session started: {session_id}")
    print()
    
    # Step 2: Agent greeting
    print_message("AGENT", agent_greeting)
    print()
    
    # Step 3: User reports the problem
    user_message_1 = "Hi, my car won't start. I think the battery is dead."
    print_message("USER", user_message_1)
    
    agent_response_1, is_complete_1 = send_message(session_id, user_message_1)
    if agent_response_1:
        print_message("AGENT", agent_response_1)
        print()
    
    # Step 4: User asks about timing
    if not is_complete_1:
        user_message_2 = "Great, how long will it take?"
        print_message("USER", user_message_2)
        
        agent_response_2, is_complete_2 = send_message(session_id, user_message_2)
        if agent_response_2:
            print_message("AGENT", agent_response_2)
            print()
    
    # Scenario summary
    print_separator()
    print("📊 SCENARIO 2 SUMMARY:")
    print("   ✅ Vehicle issue detected and classified as RSA_NEED")
    print("   ✅ Roadside Assistance service contacted")
    print("   ✅ User provided with service ticket and technician contact")
    print("   ✅ Standard response initiated (30-45 minute ETA)")
    print_separator()


def main():
    """
    Main execution function that runs both demonstration scenarios.
    """
    print_separator("BOSCH eCALL EMERGENCY COMMUNICATION SYSTEM")
    print("🚗 AI-Driven Emergency Response Demonstration")
    print(f"📡 Server URL: {BASE_URL}")
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print_separator()
    
    # Check server health before starting
    print("🔍 Checking server connectivity...")
    if not check_server_health():
        print("\n❌ Cannot proceed with demonstration. Please start the server first.")
        print("   Command: python3 -m uvicorn app.main:app --reload")
        return
    
    print_separator()
    
    try:
        # Run Scenario 1: Injury Accident
        run_injury_accident_scenario()
        
        # Brief pause between scenarios
        print("\n⏳ Pausing between scenarios...")
        time.sleep(2)
        
        # Run Scenario 2: Roadside Assistance
        run_rsa_scenario()
        
        # Final summary
        print_separator("DEMONSTRATION COMPLETE")
        print("🎉 Both scenarios executed successfully!")
        print("📋 System demonstrated:")
        print("   • Emergency incident classification")
        print("   • PSAP integration for critical situations")
        print("   • RSA coordination for vehicle breakdowns")
        print("   • Conversational state management")
        print("   • Real-time response processing")
        print()
        print("✅ The Bosch eCall Emergency Communication System is working correctly!")
        print_separator()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error during demonstration: {e}")


if __name__ == "__main__":
    main()
