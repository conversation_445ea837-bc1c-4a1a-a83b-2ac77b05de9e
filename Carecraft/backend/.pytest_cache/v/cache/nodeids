["tests/test_integration.py::TestSystemIntegration::test_complete_injury_accident_workflow", "tests/test_integration.py::TestSystemIntegration::test_complete_rsa_workflow", "tests/test_integration.py::TestSystemIntegration::test_conversation_context_preservation", "tests/test_integration.py::TestSystemIntegration::test_llm_service_error_handling", "tests/test_integration.py::TestSystemIntegration::test_metrics_tracking", "tests/test_integration.py::TestSystemIntegration::test_session_id_consistency", "tests/test_integration.py::TestSystemIntegration::test_unclear_situation_clarification", "tests/test_llm_service.py::TestLLMService::test_classify_incident_injury_accident", "tests/test_llm_service.py::TestLLMService::test_classify_incident_light_accident", "tests/test_llm_service.py::TestLLMService::test_classify_incident_road_hazard", "tests/test_llm_service.py::TestLLMService::test_classify_incident_rsa_need", "tests/test_llm_service.py::TestLLMService::test_conversation_history_formatting", "tests/test_llm_service.py::TestLLMService::test_fallback_on_api_error", "tests/test_llm_service.py::TestLLMService::test_generate_greeting", "tests/test_llm_service.py::TestLLMService::test_generate_response_with_context", "tests/test_llm_service.py::TestLLMService::test_llm_service_initialization", "tests/test_services.py::TestPSAPService::test_contact_psap_injury_accident", "tests/test_services.py::TestPSAPService::test_contact_psap_light_accident", "tests/test_services.py::TestPSAPService::test_contact_psap_road_hazard", "tests/test_services.py::TestPSAPService::test_get_psap_status", "tests/test_services.py::TestPSAPService::test_psap_llm_fallback", "tests/test_services.py::TestPSAPService::test_psap_response_message_generation", "tests/test_services.py::TestRSAService::test_contact_rsa_dead_battery", "tests/test_services.py::TestRSAService::test_contact_rsa_flat_tire", "tests/test_services.py::TestRSAService::test_contact_rsa_lockout", "tests/test_services.py::TestRSAService::test_contact_rsa_tow_needed", "tests/test_services.py::TestRSAService::test_get_rsa_status", "tests/test_services.py::TestRSAService::test_rsa_llm_fallback", "tests/test_services.py::TestRSAService::test_rsa_service_instructions", "tests/test_services.py::TestRSAService::test_rsa_urgency_handling", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_injury_accident", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_rsa_need", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_classify_incident_unknown", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_conversation_history_preservation", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_end_interaction", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_handle_unknown", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_llm_integration_in_nodes", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_injury_accident", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_light_accident", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_road_hazard", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_process_rsa_request", "tests/test_workflow_nodes.py::TestWorkflowNodes::test_start_interaction"]