"""
Simple FastAPI Server for Simple Chat Interface

This is a simplified version that doesn't require complex dependencies.
"""

import json
import asyncio
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uuid
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mock workflow functions
def run_workflow_step(state: Dict[str, Any]) -> Dict[str, Any]:
    """Mock workflow step that returns a simple response."""
    user_input = state.get("user_input", "").lower()
    
    # Simple response logic
    if "hello" in user_input or "hi" in user_input or "hey" in user_input:
        response = "Hello! This is Bosch Emergency Services. I'm here to help you. Can you please tell me what's happening?"
    elif "help" in user_input or "emergency" in user_input:
        response = "I understand you need help. Can you describe your situation? Are you injured or is anyone else injured?"
    elif "injury" in user_input or "hurt" in user_input or "injured" in user_input:
        response = "I'm dispatching emergency medical services to your location immediately. Please stay on the line. Are you conscious and able to speak?"
    elif "car" in user_input or "vehicle" in user_input or "breakdown" in user_input:
        response = "I understand you're having vehicle trouble. I'm contacting roadside assistance for you. Are you in a safe location?"
    elif "thank" in user_input:
        response = "You're welcome. Is there anything else I can help you with today?"
    elif "bye" in user_input or "goodbye" in user_input:
        response = "Take care and stay safe. Don't hesitate to contact us again if you need assistance."
        state["is_complete"] = True
    else:
        response = "I understand. Can you provide more details about your situation so I can better assist you?"
    
    # Update state
    state["final_response"] = response
    if "history" not in state:
        state["history"] = []
    
    # Add to history
    if user_input:
        state["history"].append((user_input, response))
    
    return state

def create_initial_state(session_id: str, initial_message: str = "") -> Dict[str, Any]:
    """Create initial state for a session."""
    return {
        "user_input": initial_message,
        "incident_type": None,
        "history": [],
        "next_action": None,
        "final_response": "Hello, this is Bosch Emergency Services. We've detected a potential emergency situation with your vehicle. I'm here to help you get the assistance you need. Can you please tell me what's happening right now?",
        "is_complete": False,
        "session_id": session_id,
        "context": {},
        "metrics": {}
    }

app = FastAPI(
    title="Simple Chat Emergency System",
    description="Simple chat interface for emergency communication",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory session storage
sessions: Dict[str, Dict[str, Any]] = {}

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.session_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.session_connections[session_id] = websocket
        logger.info(f"WebSocket connected for session: {session_id}")

    def disconnect(self, websocket: WebSocket, session_id: str):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if session_id in self.session_connections:
            del self.session_connections[session_id]
        logger.info(f"WebSocket disconnected for session: {session_id}")

    async def send_personal_message(self, message: dict, session_id: str):
        if session_id in self.session_connections:
            websocket = self.session_connections[session_id]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {session_id}: {e}")

manager = ConnectionManager()

@app.get("/")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "Simple Chat Emergency Communication System",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.websocket("/simple-chat/{session_id}")
async def simple_chat_websocket(websocket: WebSocket, session_id: str):
    """Simple WebSocket endpoint for conversation-only interface."""
    await manager.connect(websocket, session_id)
    
    try:
        # Send initial connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        }))
        
        # Send initial greeting if this is a new session
        if session_id not in sessions:
            initial_state = create_initial_state(session_id, "")
            updated_state = run_workflow_step(initial_state)
            sessions[session_id] = updated_state
            
            # Send initial greeting
            await websocket.send_text(json.dumps({
                "type": "agent_message",
                "session_id": session_id,
                "message": updated_state.get("final_response", ""),
                "timestamp": datetime.now().isoformat()
            }))
        
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message["type"] == "user_message":
                # Process user message through AI workflow
                user_message = message["message"]
                
                # Get current state and process message
                if session_id in sessions:
                    current_state = sessions[session_id]
                    current_state["user_input"] = user_message
                    updated_state = run_workflow_step(current_state)
                    sessions[session_id] = updated_state
                    
                    # Send only the agent's response
                    await websocket.send_text(json.dumps({
                        "type": "agent_message",
                        "session_id": session_id,
                        "message": updated_state.get("final_response", ""),
                        "is_complete": updated_state.get("is_complete", False),
                        "timestamp": datetime.now().isoformat()
                    }))
                
            elif message["type"] == "ping":
                # Respond to ping with pong for heartbeat
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, session_id)
        logger.info(f"Simple chat client {session_id} disconnected")
    except Exception as e:
        logger.error(f"Simple chat WebSocket error for session {session_id}: {e}")
        manager.disconnect(websocket, session_id)

@app.get("/simple-chat/{session_id}/history")
def get_simple_chat_history(session_id: str):
    """Get conversation history for simple chat interface."""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    state = sessions[session_id]
    history = state.get("history", [])
    
    # Format history for simple chat interface
    conversation = []
    for human_msg, ai_msg in history:
        if human_msg:  # Only add if there's a user message
            conversation.append({
                "type": "user_message",
                "message": human_msg,
                "timestamp": datetime.now().isoformat()
            })
        if ai_msg:  # Only add if there's an agent message
            conversation.append({
                "type": "agent_message", 
                "message": ai_msg,
                "timestamp": datetime.now().isoformat()
            })
    
    return {
        "session_id": session_id,
        "conversation": conversation,
        "is_complete": state.get("is_complete", False)
    }

@app.get("/ws/status")
def websocket_status():
    """Get WebSocket connection status."""
    return {
        "active_connections": len(manager.active_connections),
        "session_connections": list(manager.session_connections.keys()),
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
